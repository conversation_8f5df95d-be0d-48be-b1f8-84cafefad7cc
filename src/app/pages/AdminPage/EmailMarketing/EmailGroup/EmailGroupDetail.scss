.email-group-detail-container {
  background-color: var(--white);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  .email-group-detail__header {
    padding: 16px 20px;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    display: flex;
    align-items: center;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .header-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .header-title {
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
        color: var(--typo-colours-primary-black);
      }

      .header-description {
        font-size: 14px;
        line-height: 20px;
        color: var(--typo-colours-secondary-grey);
      }
    }
  }

  // Shared styles for both group info and conditions containers
  .group-info-container,
  .group-conditions-container {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    }

    .info-header {
      background-color: #f8fafc;
      padding: 20px 32px;
      border-bottom: 1px solid #edf2f7;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f1f1f;
        margin: 0;
        display: flex;
        align-items: center;

        &:before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 18px;
          background-color: var(--primary-color);
          margin-right: 12px;
          border-radius: 2px;
        }
      }
    }

    .info-content {
      padding: 32px;

      // Shared form styles
      .form-group-info,
      .form-group-conditions {
        display: flex;
        flex-direction: column;
        gap: 30px;

        .form-row {
          display: flex;
          flex-direction: row;
          gap: 24px;
          align-items: flex-start;

          .title-field {
            flex: 2;
          }

          .type-field {
            flex: 1;
          }
        }

        .ant-form-item {
          margin: 0;
          width: 100%;

          .ant-form-item-label > label {
            font-weight: 600;
            color: #333;
            font-size: 15px;
          }

          .ant-input, .ant-select, .ant-input-number {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s;

            &:hover, &:focus {
              border-color: var(--primary-color);
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
            }
          }

          .ant-input, .ant-input-number {
            height: 46px;
          }

          .ant-input-number {
            width: 100%;
          }

          .ant-select-selector {
            border-radius: 8px;
            height: 46px !important;
            display: flex;
            align-items: center;
          }

          .ant-select-selection-item {
            line-height: 46px;
          }

          .ant-select-multiple .ant-select-selector {
            height: auto !important;
            min-height: 46px !important;
            padding: 5px 4px;
          }

          .ant-select-multiple .ant-select-selection-item {
            line-height: 24px;
            margin-top: 2px;
            margin-bottom: 2px;
          }

          &.range-field {
            .ant-row {
              margin-bottom: 0;
            }

            .ant-input-number {
              border-radius: 8px;
              height: 46px;

              .ant-input-number-input {
                text-align: left;
                height: 44px;
                padding-left: 11px;
              }

              .ant-input-number-input-wrap {
                width: 100%;
              }

              &::placeholder,
              input::placeholder,
              .ant-input-number-placeholder {
                text-align: left;
                color: rgba(0, 0, 0, 0.45);
              }
            }
          }
        }
      }
    }
  }

  /* Additional styles for conditions */
  .group-conditions-container {
    .form-group-conditions {
      .ant-form-item {
        .ant-picker {
          width: 100%;
          height: 46px;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          transition: all 0.3s;

          &:hover, &:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }
      }
    }
  }

  .save-button-container {
    display: flex;
    justify-content: center;
    margin: 24px 0;
    width: 100%;

    .save-button,
    .cancel-button {
      min-width: 180px;
      height: 48px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      border-radius: 6px;
      transition: all 0.3s ease;
    }

    .save-button {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }

    .cancel-button {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  // User List Section Styles
  .user-list-section {
    margin-top: 32px;

    .user-list-button-container {
      display: flex;
      justify-content: center;
      padding: 24px 0;

      .toggle-user-list-button {
        min-width: 280px;
        height: 48px;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
        border: 2px solid #e2e8f0;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          border-color: var(--primary-color);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }
      }
    }

    .user-list-container {
      background-color: #ffffff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border: 1px solid #edf2f7;
      margin-top: 16px;

      .info-header {
        background-color: #f8fafc;
        padding: 20px 32px;
        border-bottom: 1px solid #edf2f7;
        border-radius: 12px 12px 0 0;

        .section-title {
          font-size: 18px;
          font-weight: 600;
          color: #1f1f1f;
          margin: 0;
          display: flex;
          align-items: center;

          &:before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 18px;
            background-color: var(--primary-color);
            margin-right: 12px;
            border-radius: 2px;
          }
        }
      }

      .info-content {
        padding: 24px 32px;

        .error-message {
          text-align: center;
          padding: 40px 20px;

          p {
            font-size: 16px;
            margin: 0;
          }
        }

        .user-list-table {
          .ant-table-thead > tr > th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
            color: #374151;
            padding: 16px 12px;

            &:first-child {
              border-radius: 8px 0 0 0;
            }

            &:last-child {
              border-radius: 0 8px 0 0;
            }
          }

          .ant-table-tbody > tr > td {
            padding: 16px 12px;
            border-bottom: 1px solid #f1f5f9;
            transition: background-color 0.2s ease;
          }

          .ant-table-tbody > tr:hover > td {
            background-color: #f8fafc;
          }

          .ant-table-tbody > tr:last-child > td {
            border-bottom: none;
          }

          .ant-pagination {
            margin-top: 24px;
            text-align: center;

            .ant-pagination-item {
              border-radius: 6px;
              border: 1px solid #e2e8f0;

              &:hover {
                border-color: var(--primary-color);
              }

              &.ant-pagination-item-active {
                background-color: var(--primary-color);
                border-color: var(--primary-color);
              }
            }
          }
        }
      }
    }
  }
}
