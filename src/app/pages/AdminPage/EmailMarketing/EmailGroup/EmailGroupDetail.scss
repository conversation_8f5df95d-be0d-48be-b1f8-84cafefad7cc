.email-group-detail-container {
  background-color: var(--white);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  .email-group-detail__header {
    padding: 16px 20px;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    display: flex;
    align-items: center;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .header-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .header-title {
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
        color: var(--typo-colours-primary-black);
      }

      .header-description {
        font-size: 14px;
        line-height: 20px;
        color: var(--typo-colours-secondary-grey);
      }
    }
  }

  // Shared styles for both group info and conditions containers
  .group-info-container,
  .group-conditions-container {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    }

    .info-header {
      background-color: #f8fafc;
      padding: 20px 32px;
      border-bottom: 1px solid #edf2f7;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f1f1f;
        margin: 0;
        display: flex;
        align-items: center;

        &:before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 18px;
          background-color: var(--primary-color);
          margin-right: 12px;
          border-radius: 2px;
        }
      }
    }

    .info-content {
      padding: 32px;

      // Shared form styles
      .form-group-info,
      .form-group-conditions {
        display: flex;
        flex-direction: column;
        gap: 30px;

        .form-row {
          display: flex;
          flex-direction: row;
          gap: 24px;
          align-items: flex-start;

          .title-field {
            flex: 2;
          }

          .type-field {
            flex: 1;
          }
        }

        .ant-form-item {
          margin: 0;
          width: 100%;

          .ant-form-item-label > label {
            font-weight: 600;
            color: #333;
            font-size: 15px;
          }

          .ant-input, .ant-select, .ant-input-number {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s;

            &:hover, &:focus {
              border-color: var(--primary-color);
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
            }
          }

          .ant-input, .ant-input-number {
            height: 46px;
          }

          .ant-input-number {
            width: 100%;
          }

          .ant-select-selector {
            border-radius: 8px;
            height: 46px !important;
            display: flex;
            align-items: center;
          }

          .ant-select-selection-item {
            line-height: 46px;
          }

          .ant-select-multiple .ant-select-selector {
            height: auto !important;
            min-height: 46px !important;
            padding: 5px 4px;
          }

          .ant-select-multiple .ant-select-selection-item {
            line-height: 24px;
            margin-top: 2px;
            margin-bottom: 2px;
          }

          &.range-field {
            .ant-row {
              margin-bottom: 0;
            }

            .ant-input-number {
              border-radius: 8px;
              height: 46px;

              .ant-input-number-input {
                text-align: left;
                height: 44px;
                padding-left: 11px;
              }

              .ant-input-number-input-wrap {
                width: 100%;
              }

              &::placeholder,
              input::placeholder,
              .ant-input-number-placeholder {
                text-align: left;
                color: rgba(0, 0, 0, 0.45);
              }
            }
          }
        }
      }
    }
  }

  /* Additional styles for conditions */
  .group-conditions-container {
    .form-group-conditions {
      .ant-form-item {
        .ant-picker {
          width: 100%;
          height: 46px;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          transition: all 0.3s;

          &:hover, &:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }
      }
    }
  }

  .save-button-container {
    display: flex;
    justify-content: center;
    margin: 24px 0;
    width: 100%;

    .save-button,
    .cancel-button {
      min-width: 180px;
      height: 48px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      border-radius: 6px;
      transition: all 0.3s ease;
    }

    .save-button {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }

    .cancel-button {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  // User List Section Styles - Sử dụng Collapse trong Card
  .user-list-collapse {
    border: none !important;
    //background-color: transparent !important;

    .ant-collapse-item {
      border: none !important;

      .ant-collapse-header {
        padding: 16px 0 !important;
        background-color: white !important;
        border-radius: 0 !important;

        .collapse-header {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 600;
          color: #1f1f1f;

          .user-count-badge {
            margin-left: 8px;
            //background-color: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
          }
        }
      }

      .ant-collapse-content {
        border-top: none !important;
        border-radius: 0 !important;
        //background-color: transparent !important;

        .ant-collapse-content-box {
          padding: 16px 0 0 0 !important;
        }
      }
    }
  }

  .user-list-content {
    padding: 0; // Không cần padding vì đã có trong Card

    .error-message {
      text-align: center;
      padding: 40px 20px;

      p {
        font-size: 16px;
        margin: 0;
      }
    }

  }

  // Table styles
  .email-marketing-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
      border-radius: 8px;
    }

    .ant-table-thead > tr > th {
      background-color: #f8fafc;
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      padding: 16px;
      border-bottom: 2px solid #edf2f7;

      &:first-child {
        padding-left: 24px;
      }

      &:last-child {
        padding-right: 24px;
      }
    }

    .ant-table-tbody > tr > td {
      padding: 16px;
      vertical-align: middle;
      white-space: normal;
      word-break: break-word;
      border-bottom: 1px solid #edf2f7;

      &:first-child {
        padding-left: 24px;
      }

      &:last-child {
        padding-right: 24px;
      }
    }

    .email-marketing-table-row {
      transition: all 0.3s ease;

      &:hover {
        background-color: #f8fafc;
      }

      &:last-child > td {
        border-bottom: none;
      }
    }

    .email-marketing-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      gap: 4px;

      .ant-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        padding: 0;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateY(0);
        }

        &:disabled {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;

          &:hover {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);
            transform: none;
            box-shadow: none;
          }
        }
      }

      // Edit button - Blue theme (similar to audio button in SpeakingPart1)
      .btn-edit {
        background-color: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;

        &:hover, &:focus {
          background-color: #bae7ff;
          border-color: #1890ff;
          color: #1890ff;
        }
      }

      // Preview button - Purple theme (similar to AI hint button)
      .btn-preview {
        background-color: #f0f2ff;
        border-color: #7b61ff;
        color: #3a18ce;

        &:hover, &:focus {
          background-color: #e6e9ff;
          border-color: #3a18ce;
          color: #3a18ce;
          box-shadow: 0 2px 8px rgba(58, 24, 206, 0.2);
        }
      }

      // Delete button - Red theme (similar to dangerous button)
      .btn-delete {
        background-color: #fff;
        border-color: #ff4d4f;
        color: #ff4d4f;

        &:hover, &:focus {
          background-color: #ff4d4f;
          border-color: #ff4d4f;
          color: #fff;
        }
      }

      // Toggle status buttons
      .btn-toggle-status {
        &.ant-btn-success {
          background-color: #f6ffed;
          border-color: #52c41a;
          color: #52c41a;

          &:hover, &:focus {
            background-color: #52c41a;
            border-color: #52c41a;
            color: #ffffff;
          }
        }

        &.ant-btn-warning {
          background-color: #fffbe6;
          border-color: #faad14;
          color: #faad14;

          &:hover, &:focus {
            background-color: #faad14;
            border-color: #faad14;
            color: #ffffff;
          }
        }
      }

      // Statistics button - Primary theme
      .btn-statistics {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;

        &:hover, &:focus {
          background-color: #40a9ff;
          border-color: #40a9ff;
          color: #fff;
        }
      }

      // Refresh button - Default theme
      .btn-refresh {
        background-color: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;

        &:hover, &:focus {
          background-color: #bae7ff;
          border-color: #1890ff;
          color: #1890ff;
        }
      }

      // Status action buttons
      .action-button {
        margin: 0 2px;

        // Primary button (Start/Resume) - Green theme
        &.ant-btn-primary {
          background-color: #f6ffed;
          border-color: #52c41a;
          color: #52c41a;

          &:hover, &:focus {
            background-color: #52c41a;
            border-color: #52c41a;
            color: #ffffff;
          }
        }

        // Default button (Pause/Reset) - Gray theme
        &.ant-btn-default {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: #595959;

          &:hover, &:focus {
            background-color: #d9d9d9;
            border-color: #d9d9d9;
            color: #262626;
          }
        }

        // Success button (Complete) - Blue theme
        &.ant-btn-success {
          background-color: #e6f7ff;
          border-color: #1890ff;
          color: #1890ff;

          &:hover, &:focus {
            background-color: #1890ff;
            border-color: #1890ff;
            color: #ffffff;
          }
        }

        // Warning button (Schedule) - Orange theme
        &.ant-btn-warning {
          background-color: #fff7e6;
          border-color: #fa8c16;
          color: #fa8c16;

          &:hover, &:focus {
            background-color: #fa8c16;
            border-color: #fa8c16;
            color: #ffffff;
          }
        }
      }
    }
  }
}
