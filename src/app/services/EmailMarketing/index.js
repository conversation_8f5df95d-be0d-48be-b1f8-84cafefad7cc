import { API } from "@api";
import {
  createBase,
  deleteBase,
  getAllBase,
  getAllPaginationBase,
  getDetailBase,
  updateBase,
  postBase,
  getBase
} from "@services/Base";
import axios from "axios";

// <PERSON><PERSON>n dịch email
export function getPaginationCampaigns(paging, query, searchFields = ["name"], populateOpts = ["targetGroups", "templateId"]) {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.EMAIL_CAMPAIGN, paging, query, searchFields, populateOpts);
}

export function getCampaignDetail(id, populateOpts = ["targetGroups", "templateId"]) {
  return getDetailBase(API.EMAIL_CAMPAIGN_ID, id, populateOpts);
}

export function createCampaign(data) {
  return createBase(API.EMAIL_CAMPAIGN, data);
}

export function updateCampaign(data) {
  return updateBase(API.EMAIL_CAMPAIGN_ID, data);
}

export function deleteCampaign(id) {
  return deleteBase(API.EMAIL_CAMPAIGN_ID, id);
}

export function updateCampaignStatus(id, status) {
  return updateBase(API.EMAIL_CAMPAIGN_STATUS, { _id: id, status });
}

export function getCampaignStatistics(id) {
  return getDetailBase(API.EMAIL_CAMPAIGN_STATISTICS, id);
}

export function sendCampaignNow(id) {
  return postBase(API.EMAIL_CAMPAIGN_SEND_NOW.format(id), {});
}

// Nhóm người dùng
export function getPaginationGroups(paging, query, searchFields = ["name"], populateOpts = []) {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.EMAIL_GROUP, paging, query, searchFields, populateOpts);
}

export function getGroupDetail(id, populateOpts = []) {
  return getDetailBase(API.EMAIL_GROUP_ID, id, populateOpts);
}

export function createGroup(data) {
  return createBase(API.EMAIL_GROUP, data);
}

export function updateGroup(data) {
  return updateBase(API.EMAIL_GROUP_ID, data, [], true, false, { hideNoti: true });
}

export function deleteGroup(id) {
  return deleteBase(API.EMAIL_GROUP_ID, id);
}

export function refreshGroup(id) {
  return updateBase(API.EMAIL_GROUP_REFRESH, { _id: id });
}

export function getUsersByAutomaticType(automaticType) {
  return getBase(API.EMAIL_GROUP_USERS_BY_TYPE, { automaticType });
}

// Mẫu email
export function getPaginationTemplates(paging, query, searchFields = ["name", "subject"], populateOpts = []) {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.EMAIL_TEMPLATE, paging, query, searchFields, populateOpts);
}

export function getTemplateDetail(id, populateOpts = []) {
  return getDetailBase(API.EMAIL_TEMPLATE_ID, id, populateOpts);
}

export function createTemplate(data) {
  return createBase(API.EMAIL_TEMPLATE, data);
}

export function updateTemplate(data) {
  return updateBase(API.EMAIL_TEMPLATE_ID, data);
}

export function deleteTemplate(id) {
  return deleteBase(API.EMAIL_TEMPLATE_ID, id);
}

// Thống kê
export function getEmailStatistics(query) {
  return axios
    .get(API.EMAIL_STATISTICS, { params: query })
    .then((response) => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return null;
    });
}

// Gửi email test
export function sendTestEmail(data) {
  return postBase(API.EMAIL_TEST_SEND, data);
}
